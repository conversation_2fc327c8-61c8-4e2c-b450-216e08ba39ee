export interface StockItem {
  id: string
  name: string
  type: string
  gsm: number
  bf: number
  size: string
  quantity: number
  price: number
  supplier: string
  location: string
  lastUpdated: string
  createdAt: string
}

export interface StockQueryParams {
  page?: number
  limit?: number
  search?: string
  type?: string
  gsm?: number
  bf?: number
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

interface StockResponse {
  items: StockItem[]
  pagination: {
    totalItems: number
    totalPages: number
    currentPage: number
    itemsPerPage: number
  }
}

const stockService = {
  getAllStockItems: async (params: StockQueryParams = {}): Promise<StockResponse> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.get('/stock', { params });
      // return response.data;

      // For demo purposes, we'll return mock data
      return {
        items: mockStockItems,
        pagination: {
          totalItems: mockStockItems.length,
          totalPages: 1,
          currentPage: params.page || 1,
          itemsPerPage: params.limit || 10,
        },
      }
    } catch (error) {
      console.error("Error fetching stock items:", error)
      throw error
    }
  },

  getStockItemById: async (id: string): Promise<StockItem> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.get(`/stock/${id}`);
      // return response.data;

      // For demo purposes, we'll return mock data
      const item = mockStockItems.find((item) => item.id === id)
      if (!item) {
        throw new Error("Stock item not found")
      }
      return item
    } catch (error) {
      console.error(`Error fetching stock item with ID ${id}:`, error)
      throw error
    }
  },

  createStockItem: async (stockItem: Omit<StockItem, "id" | "createdAt" | "lastUpdated">): Promise<StockItem> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.post('/stock', stockItem);
      // return response.data;

      // For demo purposes, we'll return mock data
      const newItem: StockItem = {
        id: `stock-${Date.now()}`,
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        ...stockItem,
      }
      mockStockItems.push(newItem)
      return newItem
    } catch (error) {
      console.error("Error creating stock item:", error)
      throw error
    }
  },

  updateStockItem: async (id: string, stockItem: Partial<StockItem>): Promise<StockItem> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.put(`/stock/${id}`, stockItem);
      // return response.data;

      // For demo purposes, we'll update mock data
      const index = mockStockItems.findIndex((item) => item.id === id)
      if (index === -1) {
        throw new Error("Stock item not found")
      }

      mockStockItems[index] = {
        ...mockStockItems[index],
        ...stockItem,
        lastUpdated: new Date().toISOString(),
      }

      return mockStockItems[index]
    } catch (error) {
      console.error(`Error updating stock item with ID ${id}:`, error)
      throw error
    }
  },

  deleteStockItem: async (id: string): Promise<void> => {
    try {
      // In a real app, this would be an API call
      // await apiClient.delete(`/stock/${id}`);

      // For demo purposes, we'll update mock data
      const index = mockStockItems.findIndex((item) => item.id === id)
      if (index === -1) {
        throw new Error("Stock item not found")
      }

      mockStockItems.splice(index, 1)
    } catch (error) {
      console.error(`Error deleting stock item with ID ${id}:`, error)
      throw error
    }
  },

  importStockItems: async (items: Omit<StockItem, "id" | "createdAt" | "lastUpdated">[]): Promise<StockItem[]> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.post('/stock/import', { items });
      // return response.data;

      // For demo purposes, we'll update mock data
      const newItems = items.map((item) => ({
        id: `stock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        ...item,
      }))

      mockStockItems.push(...newItems)
      return newItems
    } catch (error) {
      console.error("Error importing stock items:", error)
      throw error
    }
  },

  exportStockItems: async (): Promise<StockItem[]> => {
    try {
      // In a real app, this would be an API call
      // const response = await apiClient.get('/stock/export');
      // return response.data;

      // For demo purposes, we'll return mock data
      return mockStockItems
    } catch (error) {
      console.error("Error exporting stock items:", error)
      throw error
    }
  },
}

// Mock data for demonstration
const mockStockItems: StockItem[] = [
  {
    id: "stock-1",
    name: "Kraft Paper Roll",
    type: "Brown Kraft",
    gsm: 80,
    bf: 16,
    size: "48\" x 1000'",
    quantity: 50,
    price: 120.0,
    supplier: "Paper Mills Inc.",
    location: "Warehouse A",
    lastUpdated: "2023-05-15T10:30:00Z",
    createdAt: "2023-01-10T08:15:00Z",
  },
  {
    id: "stock-2",
    name: "Kraft Paper Sheets",
    type: "Natural Kraft",
    gsm: 100,
    bf: 20,
    size: '24" x 36"',
    quantity: 1000,
    price: 0.25,
    supplier: "EcoPaper Co.",
    location: "Warehouse B",
    lastUpdated: "2023-05-20T14:45:00Z",
    createdAt: "2023-02-05T11:30:00Z",
  },
  {
    id: "stock-3",
    name: "Kraft Cardboard",
    type: "Corrugated Kraft",
    gsm: 200,
    bf: 32,
    size: '36" x 48"',
    quantity: 200,
    price: 1.5,
    supplier: "BoxCraft Solutions",
    location: "Warehouse A",
    lastUpdated: "2023-05-18T09:15:00Z",
    createdAt: "2023-01-25T13:45:00Z",
  },
  {
    id: "stock-4",
    name: "Kraft Paper Bags",
    type: "Brown Kraft",
    gsm: 70,
    bf: 14,
    size: '10" x 15"',
    quantity: 5000,
    price: 0.1,
    supplier: "GreenPack Ltd.",
    location: "Warehouse C",
    lastUpdated: "2023-05-22T16:30:00Z",
    createdAt: "2023-03-12T10:00:00Z",
  },
  {
    id: "stock-5",
    name: "Kraft Wrapping Paper",
    type: "Natural Kraft",
    gsm: 60,
    bf: 12,
    size: "30\" x 500'",
    quantity: 25,
    price: 45.0,
    supplier: "Paper Mills Inc.",
    location: "Warehouse B",
    lastUpdated: "2023-05-17T11:20:00Z",
    createdAt: "2023-02-18T09:30:00Z",
  },
]

export default stockService
