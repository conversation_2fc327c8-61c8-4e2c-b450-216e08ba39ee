import prisma from '../config/database';
import { AppError } from '../utils/errorHandler';
import { getUserCart, clearCart, isCartEmpty } from './cart.service';
import { getStockById, updateStock } from './stock.service';
import { createNotification } from './notification.service';
import { Order, OrderStatus, PaymentStatus, PaymentTerms } from '@prisma/client';
import { generateOrderNumber } from '../utils/orderNumber';

/**
 * Get user's orders with pagination and filtering
 * @param userId - User ID
 * @param options - Query options for filtering and pagination
 * @returns Paginated orders
 */
export const getUserOrders = async (
  userId: string,
  options: {
    page?: number;
    limit?: number;
    status?: OrderStatus;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = { userId };
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get orders with pagination
  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        items: {
          include: {
            stock: {
              select: {
                type: true,
                gsm: true,
                bf: true,
              },
            },
          },
        },
      },
    }),
    prisma.order.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    orders,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get all orders (admin only) with pagination and filtering
 * @param options - Query options for filtering and pagination
 * @returns Paginated orders
 */
export const getAllOrders = async (
  options: {
    page?: number;
    limit?: number;
    status?: OrderStatus;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get orders with pagination
  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        rider: {
          select: {
            id: true,
            companyName: true,
            contactPerson: true,
            email: true,
            contactNumber: true,
          },
        },
        items: {
          include: {
            stock: {
              select: {
                type: true,
                gsm: true,
                bf: true,
              },
            },
          },
        },
      },
    }),
    prisma.order.count({ where }),
  ]);
  console.log("orders are :", orders);
  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    orders,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get order by ID
 * @param orderId - Order ID
 * @param userId - User ID (optional, for authorization)
 * @returns Order with items
 */
export const getOrderById = async (orderId: string, userId?: string): Promise<Order> => {
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    include: {
      items: {
        include: {
          stock: {
            select: {
              type: true,
              gsm: true,
              bf: true,
            },
          },
        },
      },
      rider: {
        select: {
          id: true,
          companyName: true,
          contactPerson: true,
          email: true,
          contactNumber: true,
        },
      },
    },
  });

  if (!order) {
    throw new AppError('Order not found', 404);
  }

  // Check if user is authorized to view this order
  if (userId && order.userId !== userId) {
    throw new AppError('You are not authorized to view this order', 403);
  }

  return order;
};

/**
 * Create new order from cart
 * @param userId - User ID
 * @param data - Order data
 * @returns Created order
 */
export const createOrder = async (
  userId: string,
  data: {
    shippingAddress: {
      addressLine1: string;
      addressLine2?: string;
      city: string;
      state: string;
      postalCode: string;
    };
    notes?: string;
  }
): Promise<Order> => {
  // Check if cart is empty
  const isEmpty = await isCartEmpty(userId);
  if (isEmpty) {
    throw new AppError('Cannot create order with empty cart', 400);
  }

  // Get user's cart with stock details
  const cart = await getUserCart(userId, true);

  // Get user's payment terms
  const user = await prisma.rider.findUnique({
    where: { id: userId },
    select: { paymentTerms: true },
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Calculate payment due date based on payment terms
  let paymentDueDate: Date | null = null;
  if (user.paymentTerms === PaymentTerms.THIRTY_DAYS) {
    paymentDueDate = new Date();
    paymentDueDate.setDate(paymentDueDate.getDate() + 30);
  } else if (user.paymentTerms === PaymentTerms.SIXTY_DAYS) {
    paymentDueDate = new Date();
    paymentDueDate.setDate(paymentDueDate.getDate() + 60);
  }

  // Generate order number
  const orderNumber = await generateOrderNumber();

  // Create order and order items in a transaction
  const order = await prisma.$transaction(async (tx) => {
    // Create order
    const newOrder = await tx.order.create({
      data: {
        orderNumber,
        userId,
        totalAmount: cart.total,
        paymentTerms: user.paymentTerms,
        paymentDueDate,
        shippingAddress: data.shippingAddress,
        notes: data.notes,
        items: {
          create: cart.items.map((item) => ({
            stockId: item.stockId,
            type: item.stock!.type,
            gsm: item.stock!.gsm,
            bf: item.stock!.bf,
            quantity: item.quantity,
            pricePerRoll: item.stock!.pricePerRoll,
            totalPrice: item.quantity * item.stock!.pricePerRoll,
          })),
        },
      },
      include: {
        items: true,
      },
    });

    // Update stock quantities
    for (const item of cart.items) {
      const stock = await tx.stock.findUnique({
        where: { id: item.stockId },
      });

      if (!stock) {
        throw new AppError(`Stock with ID ${item.stockId} not found`, 404);
      }

      if (stock.rollsAvailable < item.quantity) {
        throw new AppError(
          `Insufficient stock for ${stock.type} GSM ${stock.gsm} BF ${stock.bf}`,
          400
        );
      }

      await tx.stock.update({
        where: { id: item.stockId },
        data: {
          rollsAvailable: stock.rollsAvailable - item.quantity,
        },
      });
    }

    return newOrder;
  });

  // Clear the user's cart after successful order creation
  await clearCart(userId);

  // Create notification for user
  await createNotification(
    userId,
    'ORDER_STATUS',
    'Order Placed Successfully',
    `Your order #${orderNumber} has been placed successfully and is pending approval.`
  );

  return order;
};

/**
 * Cancel an order
 * @param orderId - Order ID
 * @param userId - User ID (for authorization)
 * @returns Cancelled order
 */
export const cancelOrder = async (orderId: string, userId: string): Promise<Order> => {
  // Get order and check ownership
  const order = await getOrderById(orderId, userId);

  // Check if order can be cancelled
  if (order.status !== OrderStatus.PENDING) {
    throw new AppError(
      'Only pending orders can be cancelled',
      400
    );
  }

  // Cancel order in a transaction
  const cancelledOrder = await prisma.$transaction(async (tx) => {
    // Update order status
    const updatedOrder = await tx.order.update({
      where: { id: orderId },
      data: {
        status: OrderStatus.CANCELLED,
        cancelledAt: new Date(),
      },
      include: {
        items: true,
      },
    });

    // Restore stock quantities
    for (const item of updatedOrder.items) {
      const stock = await tx.stock.findUnique({
        where: { id: item.stockId },
      });

      if (stock) {
        await tx.stock.update({
          where: { id: item.stockId },
          data: {
            rollsAvailable: stock.rollsAvailable + item.quantity,
          },
        });
      }
    }

    return updatedOrder;
  });

  // Create notification for user
  await createNotification(
    userId,
    'ORDER_STATUS',
    'Order Cancelled',
    `Your order #${order.orderNumber} has been cancelled.`
  );

  return cancelledOrder;
};

/**
 * Update order status (admin only)
 * @param orderId - Order ID
 * @param status - New order status
 * @returns Updated order
 */
export const updateOrderStatus = async (
  orderId: string,
  status: OrderStatus
): Promise<Order> => {
  // Get order
  const order = await getOrderById(orderId);

  // Validate status transition
  validateStatusTransition(order.status, status);

  // Update status-related timestamps
  const statusData: any = {
    status,
  };

  if (status === OrderStatus.APPROVED) {
    statusData.approvedAt = new Date();
  } else if (status === OrderStatus.SHIPPED) {
    statusData.shippedAt = new Date();
  } else if (status === OrderStatus.DELIVERED) {
    statusData.deliveredAt = new Date();
  } else if (status === OrderStatus.CANCELLED) {
    statusData.cancelledAt = new Date();
  }

  // Update order
  const updatedOrder = await prisma.order.update({
    where: { id: orderId },
    data: statusData,
    include: {
      items: true,
    },
  });

  // If order is cancelled, restore stock quantities
  if (status === OrderStatus.CANCELLED) {
    for (const item of updatedOrder.items) {
      const stock = await prisma.stock.findUnique({
        where: { id: item.stockId },
      });

      if (stock) {
        await prisma.stock.update({
          where: { id: item.stockId },
          data: {
            rollsAvailable: stock.rollsAvailable + item.quantity,
          },
        });
      }
    }
  }

  // Create notification for user
  await createNotification(
    order.userId,
    'ORDER_STATUS',
    `Order ${getStatusDisplayName(status)}`,
    `Your order #${order.orderNumber} has been ${getStatusDisplayName(status).toLowerCase()}.`
  );

  return updatedOrder;
};

/**
 * Update payment status (admin only)
 * @param orderId - Order ID
 * @param paymentStatus - New payment status
 * @returns Updated order
 */
export const updatePaymentStatus = async (
  orderId: string,
  paymentStatus: PaymentStatus
): Promise<Order> => {
  // Get order
  const order = await getOrderById(orderId);

  // Update payment status
  const updatedOrder = await prisma.order.update({
    where: { id: orderId },
    data: {
      paymentStatus,
    },
  });

  // Create notification for user
  await createNotification(
    order.userId,
    'PAYMENT_REMINDER',
    `Payment Status Updated`,
    `Payment status for your order #${order.orderNumber} has been updated to ${paymentStatus}.`
  );

  return updatedOrder;
};

/**
 * Validate order status transition
 * @param currentStatus - Current order status
 * @param newStatus - New order status
 * @throws AppError if transition is invalid
 */
const validateStatusTransition = (
  currentStatus: OrderStatus,
  newStatus: OrderStatus
): void => {
  // Define valid transitions
  const validTransitions: Record<OrderStatus, OrderStatus[]> = {
    [OrderStatus.PENDING]: [OrderStatus.APPROVED, OrderStatus.CANCELLED],
    [OrderStatus.APPROVED]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
    [OrderStatus.SHIPPED]: [OrderStatus.DELIVERED, OrderStatus.CANCELLED],
    [OrderStatus.DELIVERED]: [],
    [OrderStatus.CANCELLED]: [],
  };

  // Check if transition is valid
  if (!validTransitions[currentStatus].includes(newStatus)) {
    throw new AppError(
      `Cannot transition order from ${currentStatus} to ${newStatus}`,
      400
    );
  }
};

/**
 * Get display name for order status
 * @param status - Order status
 * @returns Display name
 */
const getStatusDisplayName = (status: OrderStatus): string => {
  const displayNames: Record<OrderStatus, string> = {
    [OrderStatus.PENDING]: 'Pending',
    [OrderStatus.APPROVED]: 'Approved',
    [OrderStatus.SHIPPED]: 'Shipped',
    [OrderStatus.DELIVERED]: 'Delivered',
    [OrderStatus.CANCELLED]: 'Cancelled',
  };

  return displayNames[status];
};
