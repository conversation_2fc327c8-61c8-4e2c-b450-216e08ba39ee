// filepath: /home/<USER>/Dev/project-kraft/backend/src/routes/auth.routes.ts
import { Router } from 'express';
import validate from '../middlewares/validate.middleware';
import {
  registerSchema,
  phoneOTPSchema,
  phoneVerificationSchema,
  emailOTPSchema,
  emailVerificationSchema,
  jwtRegisterSchema,
} from '../validations/auth.validation';
import {
  registerRider,
  
  sendPhoneOTPHandler,
  verifyPhoneOTPHandler,
  sendEmailOTPHandler,
  verifyEmailOTPHandler,
  registerRiderWithJwt,
  loginWithEmail,
} from '../controllers/auth.controller';
import { authenticateRider } from '../middlewares/auth.middleware';
import { authenticateJwt, requireRegisteredUser } from '../middlewares/jwt.middleware';

const router = Router();

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new rider account
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - gstNumber
 *               - companyName
 *               - contactPerson
 *               - contactNumber
 *               - email
 *               - firebaseUid
 *               - paymentTerms
 *             properties:
 *               gstNumber:
 *                 type: string
 *               companyName:
 *                 type: string
 *               contactPerson:
 *                 type: string
 *               contactNumber:
 *                 type: string
 *               email:
 *                 type: string
 *               firebaseUid:
 *                 type: string
 *               paymentTerms:
 *                 type: string
 *                 enum: [IMMEDIATE, THIRTY_DAYS, SIXTY_DAYS]
 */
router.post('/register', validate(registerSchema), registerRider);

/**
 * @swagger
 * /api/auth/register/jwt:
 *   post:
 *     summary: Register a new rider account with JWT authentication
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - gstNumber
 *               - companyName
 *               - contactPerson
 *               - contactNumber
 *               - email
 *               - paymentTerms
 *             properties:
 *               gstNumber:
 *                 type: string
 *               companyName:
 *                 type: string
 *               contactPerson:
 *                 type: string
 *               contactNumber:
 *                 type: string
 *               email:
 *                 type: string
 *               paymentTerms:
 *                 type: string
 *                 enum: [IMMEDIATE, THIRTY_DAYS, SIXTY_DAYS]
 */
router.post(
  '/register/jwt', 
  authenticateJwt, 
  validate(jwtRegisterSchema), 
  registerRiderWithJwt
);

/**
 * @swagger
 * /api/auth/login/firebase:
 *   post:
 *     summary: Login with Firebase token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firebaseToken
 *             properties:
 *               firebaseToken:
 *                 type: string
 */
router.post('/login/email', loginWithEmail);

/**
 * @swagger
 * /api/auth/phone/send-otp:
 *   post:
 *     summary: Send OTP to phone number
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phoneNumber
 *               - recaptchaToken
 *             properties:
 *               phoneNumber:
 *                 type: string
 *               recaptchaToken:
 *                 type: string
 */
router.post('/phone/send-otp', validate(phoneOTPSchema), sendPhoneOTPHandler);

/**
 * @swagger
 * /api/auth/phone/verify-otp:
 *   post:
 *     summary: Verify phone OTP
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - verificationId
 *               - code
 *             properties:
 *               verificationId:
 *                 type: string
 *               code:
 *                 type: string
 */
router.post('/phone/verify-otp', validate(phoneVerificationSchema), verifyPhoneOTPHandler);

/**
 * @swagger
 * /api/auth/send-otp:
 *   post:
 *     summary: Send OTP to email
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 */
router.post('/send-otp', validate(emailOTPSchema), sendEmailOTPHandler);

/**
 * @swagger
 * /api/auth/verify-otp:
 *   post:
 *     summary: Verify email OTP
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - otp
 *             properties:
 *               email:
 *                 type: string
 *               otp:
 *                 type: string
 */
router.post('/verify-otp', validate(emailVerificationSchema), verifyEmailOTPHandler);

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current rider profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 */
router.get('/me', authenticateRider, (req, res) => {
  res.json({
    status: 'success',
    data: {
      user: req.user
    }
  });
});

/**
 * @swagger
 * /api/auth/profile:
 *   get:
 *     summary: Get current rider profile using JWT authentication
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 */
router.get('/profile', authenticateJwt, requireRegisteredUser, (req, res) => {
  res.json({
    status: 'success',
    data: {
      user: req.user
    }
  });
});

export default router;
