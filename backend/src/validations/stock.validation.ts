import Joi from 'joi';

// Stock creation validation schema
export const createStockSchema = Joi.object({
  type: Joi.string().trim().required()
    .messages({
      'string.empty': 'Stock type is required',
      'any.required': 'Stock type is required',
    }),
  gsm: Joi.number().integer().min(1).required()
    .messages({
      'number.base': 'GSM must be a number',
      'number.integer': 'GSM must be an integer',
      'number.min': 'GSM must be at least 1',
      'any.required': 'GSM is required',
    }),
  bf: Joi.number().integer().min(1).required()
    .messages({
      'number.base': 'BF must be a number',
      'number.integer': 'BF must be an integer',
      'number.min': 'BF must be at least 1',
      'any.required': 'BF is required',
    }),
  rollsAvailable: Joi.number().integer().min(0).required()
    .messages({
      'number.base': 'Rolls available must be a number',
      'number.integer': 'Rolls available must be an integer',
      'number.min': 'Rolls available must be at least 0',
      'any.required': 'Rolls available is required',
    }),
  pricePerRoll: Joi.number().precision(2).min(0).required()
    .messages({
      'number.base': 'Price per roll must be a number',
      'number.min': 'Price per roll must be at least 0',
      'any.required': 'Price per roll is required',
    }),
});

// Stock update validation schema
export const updateStockSchema = Joi.object({
  rollsAvailable: Joi.number().integer().min(0)
    .messages({
      'number.base': 'Rolls available must be a number',
      'number.integer': 'Rolls available must be an integer',
      'number.min': 'Rolls available must be at least 0',
    }),
  pricePerRoll: Joi.number().precision(2).min(0)
    .messages({
      'number.base': 'Price per roll must be a number',
      'number.min': 'Price per roll must be at least 0',
    }),
}).min(1).messages({
  'object.min': 'At least one field is required for update',
});

// Stock query validation schema
export const stockQuerySchema = Joi.object({
  page: Joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  search: Joi.string().trim().allow('')
    .messages({
      'string.base': 'Search must be a string',
    }),
  type: Joi.string().trim()
    .messages({
      'string.empty': 'Stock type cannot be empty',
    }),
  gsm: Joi.number().integer().min(1)
    .messages({
      'number.base': 'GSM must be a number',
      'number.integer': 'GSM must be an integer',
      'number.min': 'GSM must be at least 1',
    }),
  bf: Joi.number().integer().min(1)
    .messages({
      'number.base': 'BF must be a number',
      'number.integer': 'BF must be an integer',
      'number.min': 'BF must be at least 1',
    }),
  minPrice: Joi.number().min(0)
    .messages({
      'number.base': 'Minimum price must be a number',
      'number.min': 'Minimum price must be at least 0',
    }),
  maxPrice: Joi.number().min(0)
    .messages({
      'number.base': 'Maximum price must be a number',
      'number.min': 'Maximum price must be at least 0',
    }),
  sortBy: Joi.string().valid('type', 'gsm', 'bf', 'rollsAvailable', 'pricePerRoll', 'createdAt')
    .messages({
      'any.only': 'Sort by must be one of: type, gsm, bf, rollsAvailable, pricePerRoll, createdAt',
    }),
  sortOrder: Joi.string().valid('asc', 'desc')
    .messages({
      'any.only': 'Sort order must be one of: asc, desc',
    }),
});
