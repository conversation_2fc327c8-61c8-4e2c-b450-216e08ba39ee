import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getStocks,
  getStockById,
  createStock,
  updateStock,
  deleteStock,
  importStockFromCSV,
} from '../services/stock.service';
import { createNotification } from '../services/notification.service';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

/**
 * Get all stock items with filtering and pagination
 */
export const getAllStocks = asyncHandler(async (req: Request, res: Response) => {
  const {
    page,
    limit,
    type,
    gsm,
    bf,
    minPrice,
    maxPrice,
    sortBy,
    sortOrder,
  } = req.query;

  const result = await getStocks({
    page: page ? parseInt(page as string) : undefined,
    limit: limit ? parseInt(limit as string) : undefined,
    type: type as string,
    gsm: gsm ? parseInt(gsm as string) : undefined,
    bf: bf ? parseInt(bf as string) : undefined,
    minPrice: minPrice ? parseFloat(minPrice as string) : undefined,
    maxPrice: maxPrice ? parseFloat(maxPrice as string) : undefined,
    sortBy: sortBy as string,
    sortOrder: sortOrder as 'asc' | 'desc',
  });

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Get stock item by ID
 */
export const getStock = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const stock = await getStockById(id);

  return res.status(200).json({
    status: 'success',
    data: { stock },
  });
});

/**
 * Create new stock item
 */
export const createStockItem = asyncHandler(async (req: Request, res: Response) => {
  const { type, gsm, bf, rollsAvailable, pricePerRoll } = req.body;

  const stock = await createStock({
    type,
    gsm: parseInt(gsm),
    bf: parseInt(bf),
    rollsAvailable: parseInt(rollsAvailable),
    pricePerRoll: parseFloat(pricePerRoll),
  });

  // Notify admin users about new stock
  await createNotification(
    req.user!.id,
    'STOCK_UPDATE',
    'New Stock Added',
    `New stock item added: ${type} GSM ${gsm} BF ${bf}`
  );

  return res.status(201).json({
    status: 'success',
    data: { stock },
  });
});

/**
 * Update stock item
 */
export const updateStockItem = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const { rollsAvailable, pricePerRoll } = req.body;

  const updateData: any = {};
  if (rollsAvailable !== undefined) updateData.rollsAvailable = parseInt(rollsAvailable);
  if (pricePerRoll !== undefined) updateData.pricePerRoll = parseFloat(pricePerRoll);

  const stock = await updateStock(id, updateData);

  // Notify admin users about stock update
  await createNotification(
    req.user!.id,
    'STOCK_UPDATE',
    'Stock Updated',
    `Stock item updated: ${stock.type} GSM ${stock.gsm} BF ${stock.bf}`
  );

  return res.status(200).json({
    status: 'success',
    data: { stock },
  });
});

/**
 * Delete stock item
 */
export const deleteStockItem = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const stock = await deleteStock(id);

  // Notify admin users about stock deletion
  await createNotification(
    req.user!.id,
    'STOCK_UPDATE',
    'Stock Deleted',
    `Stock item deleted: ${stock.type} GSM ${stock.gsm} BF ${stock.bf}`
  );

  return res.status(200).json({
    status: 'success',
    message: 'Stock item deleted successfully',
    data: { stock },
  });
});

/**
 * Import stock items from CSV file
 */
export const importStockItems = asyncHandler(async (req: Request, res: Response) => {
  console.log('Import stock items endpoint called');

  if (!req.file) {
    console.log('No file uploaded');
    throw new AppError('No file uploaded', 400);
  }

  console.log('File uploaded:', req.file.originalname, req.file.path);

  const results: any[] = [];
  const errors: { row: number; message: string }[] = [];
  let rowIndex = 1; // Start at 1 to account for header row

  try {
    // Check if file exists
    if (!fs.existsSync(req.file.path)) {
      console.error('File does not exist at path:', req.file.path);
      throw new AppError('Uploaded file not found', 500);
    }

    console.log('Starting CSV processing');

    // Process CSV file
    await new Promise<void>((resolve, reject) => {
      fs.createReadStream(req.file!.path)
        .pipe(csv())
        .on('data', (data) => {
          rowIndex++;
          console.log(`Processing row ${rowIndex}:`, data);
          results.push({
            ...data,
            rowIndex
          });
        })
        .on('end', () => {
          console.log('CSV processing completed, rows:', results.length);
          resolve();
        })
        .on('error', (error) => {
          console.error('CSV parsing error:', error);
          reject(error);
        });
    });

    console.log('CSV processing completed, starting import');

    // Process the data
    const { importedCount, failedCount } = await importStockFromCSV(results, errors, req.user!.id);

    console.log(`Import completed: ${importedCount} imported, ${failedCount} failed`);

    // Delete the temporary file
    try {
      fs.unlinkSync(req.file.path);
      console.log('Temporary file deleted');
    } catch (unlinkError) {
      console.error('Error deleting temporary file:', unlinkError);
      // Continue execution even if file deletion fails
    }

    // Return response
    if (errors.length > 0) {
      console.log('Import completed with errors:', errors);
      return res.status(400).json({
        status: 'error',
        message: 'Import completed with errors',
        success: false,
        imported: importedCount,
        failed: failedCount,
        errors
      });
    }

    return res.status(200).json({
      status: 'success',
      message: 'Stock items imported successfully',
      success: true,
      imported: importedCount,
      failed: 0
    });
  } catch (error: any) {
    console.error('Error importing stock:', error);

    // Delete the temporary file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
        console.log('Temporary file deleted after error');
      } catch (unlinkError) {
        console.error('Error deleting temporary file after error:', unlinkError);
      }
    }

    throw new AppError(error.message || 'Failed to import stock items', 500);
  }
});

/**
 * Export all stock items to CSV
 */
export const exportStockToCSV = asyncHandler(async (req: Request, res: Response) => {
  console.log('Export stock to CSV endpoint called');

  try {
    // Get all stock items without pagination
    const result = await getStocks({
      limit: 1000, // Set a high limit to get all items
    });

    const stocks = result.stocks;
    console.log(`Exporting ${stocks.length} stock items to CSV`);

    // Create CSV header
    const header = 'id,type,gsm,bf,rollsAvailable,pricePerRoll,createdAt,updatedAt\n';

    // Create CSV rows
    const rows = stocks.map(stock => {
      return `${stock.id},${stock.type},${stock.gsm},${stock.bf},${stock.rollsAvailable},${stock.pricePerRoll},${stock.createdAt},${stock.updatedAt}`;
    }).join('\n');

    // Combine header and rows
    const csvContent = header + rows;

    // Set response headers
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=stock-export.csv');

    console.log('Sending CSV export');
    return res.status(200).send(csvContent);
  } catch (error) {
    console.error('Error in exportStockToCSV:', error);
    throw new AppError('Failed to export stock data', 500);
  }
});
